@extends('admin.layout')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')
@section('page-description', 'Visão geral do sistema de raspadinhas')

@section('content')
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Lucro Total -->
    <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-dollar-sign text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Lucro</p>
                <p class="text-2xl font-bold text-green-600">R$ {{ number_format($stats['total_profit'], 2, ',', '.') }}</p>
                <p class="text-xs text-gray-500">Total acumulado</p>
            </div>
        </div>
    </div>

    <!-- Depósitos -->
    <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-credit-card text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Depósitos</p>
                <p class="text-2xl font-bold text-blue-600">R$ {{ number_format($stats['total_deposits'], 2, ',', '.') }}</p>
                <p class="text-xs text-gray-500">Hoje: R$ {{ number_format($stats['deposits_today'], 2, ',', '.') }}</p>
            </div>
        </div>
    </div>

    <!-- Saques -->
    <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 text-red-600">
                <i class="fas fa-money-bill-wave text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Saques</p>
                <p class="text-2xl font-bold text-red-600">R$ {{ number_format($stats['total_withdrawals'], 2, ',', '.') }}</p>
                <p class="text-xs text-gray-500">Hoje: R$ {{ number_format($stats['withdrawals_today'], 2, ',', '.') }}</p>
            </div>
        </div>
    </div>

    <!-- Usuários -->
    <div class="bg-white rounded-lg shadow-md p-6 card-hover transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                <i class="fas fa-users text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Usuários</p>
                <p class="text-2xl font-bold text-purple-600">{{ number_format($stats['total_users']) }}</p>
                <p class="text-xs text-gray-500">Novos hoje: {{ $stats['users_today'] }}</p>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Jogos -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Jogos</h3>
            <div class="p-2 rounded-full bg-yellow-100 text-yellow-600">
                <i class="fas fa-gamepad"></i>
            </div>
        </div>
        <div class="space-y-3">
            <div class="flex justify-between">
                <span class="text-gray-600">Total de Jogos</span>
                <span class="font-semibold">{{ number_format($stats['total_games']) }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Jogos Hoje</span>
                <span class="font-semibold text-green-600">{{ number_format($stats['games_today']) }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Jogos Este Mês</span>
                <span class="font-semibold text-blue-600">{{ number_format($stats['games_this_month']) }}</span>
            </div>
        </div>
    </div>

    <!-- Desempenho -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Desempenho</h3>
            <div class="p-2 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        <div class="space-y-3">
            <div class="flex justify-between">
                <span class="text-gray-600">Lucro Hoje</span>
                <span class="font-semibold text-green-600">R$ {{ number_format($stats['profit_today'], 2, ',', '.') }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Lucro Este Mês</span>
                <span class="font-semibold text-blue-600">R$ {{ number_format($stats['profit_this_month'], 2, ',', '.') }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Taxa de Retorno</span>
                <span class="font-semibold text-purple-600">
                    {{ $stats['total_deposits'] > 0 ? number_format(($stats['total_withdrawals'] / $stats['total_deposits']) * 100, 1) : 0 }}%
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Dados Financeiros -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Dados Financeiros</h3>
        <div class="h-64">
            <canvas id="financialChart"></canvas>
        </div>
    </div>

    <!-- Raspadinhas Mais Jogadas -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Raspadinhas Mais Jogadas</h3>
        <div class="h-64">
            <canvas id="gamesChart"></canvas>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Financial Chart
    const financialCtx = document.getElementById('financialChart').getContext('2d');
    new Chart(financialCtx, {
        type: 'doughnut',
        data: {
            labels: ['Lucro', 'Depósitos', 'Saques'],
            datasets: [{
                data: [
                    {{ $stats['total_profit'] }},
                    {{ $stats['total_deposits'] }},
                    {{ $stats['total_withdrawals'] }}
                ],
                backgroundColor: [
                    '#10b981',
                    '#3b82f6',
                    '#ef4444'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Games Chart
    const gamesCtx = document.getElementById('gamesChart').getContext('2d');
    new Chart(gamesCtx, {
        type: 'pie',
        data: {
            labels: ['Hoje', 'Esta Semana', 'Este Mês', 'Outros'],
            datasets: [{
                data: [
                    {{ $stats['games_today'] }},
                    {{ $stats['games_today'] * 7 }},
                    {{ $stats['games_this_month'] }},
                    {{ $stats['total_games'] - $stats['games_this_month'] }}
                ],
                backgroundColor: [
                    '#f59e0b',
                    '#8b5cf6',
                    '#06b6d4',
                    '#6b7280'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
@endsection
