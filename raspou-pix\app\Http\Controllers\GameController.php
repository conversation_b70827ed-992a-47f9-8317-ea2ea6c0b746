<?php

namespace App\Http\Controllers;

use App\Models\Game;
use App\Models\ScratchCard;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GameController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function purchase(Request $request, ScratchCard $scratchCard)
    {
        $user = Auth::user();

        // Check if user has enough balance
        if ($user->balance < $scratchCard->price) {
            return response()->json([
                'success' => false,
                'message' => 'Saldo insuficiente!'
            ]);
        }

        // Check if scratch card is available
        if ($scratchCard->available_cards <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'Raspadinha esgotada!'
            ]);
        }

        DB::beginTransaction();
        try {
            // Debit user balance
            $user->decrement('balance', $scratchCard->price);

            // Create purchase transaction
            Transaction::create([
                'user_id' => $user->id,
                'type' => 'game_purchase',
                'amount' => $scratchCard->price,
                'status' => 'completed',
                'description' => "Compra da raspadinha: {$scratchCard->name}"
            ]);

            // Generate game data
            $gameData = $this->generateGameData($scratchCard);

            // Create game
            $game = Game::create([
                'user_id' => $user->id,
                'scratch_card_id' => $scratchCard->id,
                'game_data' => $gameData,
                'status' => 'playing'
            ]);

            // Update scratch card sold count
            $scratchCard->increment('cards_sold');

            DB::commit();

            return response()->json([
                'success' => true,
                'game_id' => $game->id,
                'redirect' => route('games.play', $game)
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Erro ao processar compra!'
            ]);
        }
    }

    public function play(Game $game)
    {
        if ($game->user_id !== Auth::id()) {
            abort(403);
        }

        return view('games.scratch-game', compact('game'));
    }

    private function generateGameData(ScratchCard $scratchCard)
    {
        // Generate 9 positions with symbols
        $symbols = ['🍒', '🍋', '🍊', '🍇', '🔔', '💎', '⭐', '🎰'];
        $positions = [];

        // Determine if this is a winning game based on prize structure
        $isWinner = $this->determineWinner($scratchCard->prize_structure);

        if ($isWinner['win']) {
            // Create winning pattern (3 matching symbols)
            $winningSymbol = $symbols[array_rand($symbols)];
            $winningPositions = [0, 4, 8]; // Diagonal

            for ($i = 0; $i < 9; $i++) {
                if (in_array($i, $winningPositions)) {
                    $positions[$i] = $winningSymbol;
                } else {
                    $positions[$i] = $symbols[array_rand($symbols)];
                }
            }

            return [
                'positions' => $positions,
                'scratched' => array_fill(0, 9, false),
                'prize_amount' => $isWinner['amount']
            ];
        } else {
            // Create non-winning pattern
            for ($i = 0; $i < 9; $i++) {
                $positions[$i] = $symbols[array_rand($symbols)];
            }

            // Ensure no winning combinations
            while ($this->hasWinningCombination($positions)) {
                $positions[array_rand($positions)] = $symbols[array_rand($symbols)];
            }

            return [
                'positions' => $positions,
                'scratched' => array_fill(0, 9, false),
                'prize_amount' => 0
            ];
        }
    }

    private function determineWinner($prizeStructure)
    {
        $totalWeight = array_sum(array_column($prizeStructure, 'weight'));
        $random = rand(1, $totalWeight);
        $currentWeight = 0;

        foreach ($prizeStructure as $prize) {
            $currentWeight += $prize['weight'];
            if ($random <= $currentWeight) {
                return [
                    'win' => $prize['amount'] > 0,
                    'amount' => $prize['amount']
                ];
            }
        }

        return ['win' => false, 'amount' => 0];
    }

    private function hasWinningCombination($positions)
    {
        // Check rows
        for ($i = 0; $i < 3; $i++) {
            if ($positions[$i*3] === $positions[$i*3+1] && $positions[$i*3+1] === $positions[$i*3+2]) {
                return true;
            }
        }

        // Check columns
        for ($i = 0; $i < 3; $i++) {
            if ($positions[$i] === $positions[$i+3] && $positions[$i+3] === $positions[$i+6]) {
                return true;
            }
        }

        // Check diagonals
        if ($positions[0] === $positions[4] && $positions[4] === $positions[8]) {
            return true;
        }
        if ($positions[2] === $positions[4] && $positions[4] === $positions[6]) {
            return true;
        }

        return false;
    }

    public function scratch(Request $request, Game $game)
    {
        if ($game->user_id !== Auth::id() || $game->status !== 'playing') {
            return response()->json(['success' => false]);
        }

        $position = $request->input('position') - 1; // Convert from 1-9 to 0-8
        $gameData = $game->game_data;

        if ($position >= 0 && $position < 9) {
            $gameData['scratched'][$position] = true;
            $game->update(['game_data' => $gameData]);

            // Check if all positions are scratched
            $gameCompleted = !in_array(false, $gameData['scratched']);
            if ($gameCompleted) {
                $this->completeGame($game);

                // Update user balance
                $user = Auth::user();
                $user->refresh();

                // Check for new achievements
                $newAchievements = $user->checkAchievements();
            }

            return response()->json([
                'success' => true,
                'symbol' => $gameData['positions'][$position],
                'game_completed' => $gameCompleted,
                'prize_amount' => $gameData['prize_amount'],
                'new_balance' => Auth::user()->balance,
                'new_achievements' => $gameCompleted && isset($newAchievements) ? $newAchievements->map(function($achievement) {
                    return [
                        'name' => $achievement->name,
                        'description' => $achievement->description,
                        'icon' => $achievement->icon,
                        'points' => $achievement->points
                    ];
                }) : []
            ]);
        }

        return response()->json(['success' => false]);
    }

    private function completeGame(Game $game)
    {
        DB::beginTransaction();
        try {
            $game->update([
                'status' => 'completed',
                'prize_amount' => $game->game_data['prize_amount'],
                'completed_at' => now()
            ]);

            // If there's a prize, create a transaction and update balance
            if ($game->game_data['prize_amount'] > 0) {
                Transaction::create([
                    'user_id' => $game->user_id,
                    'type' => 'prize_win',
                    'amount' => $game->game_data['prize_amount'],
                    'status' => 'completed',
                    'description' => "Prêmio da raspadinha: {$game->scratchCard->name}"
                ]);

                // Update user balance
                $user = $game->user;
                $user->increment('balance', $game->game_data['prize_amount']);
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
