@extends('layouts.app')

@section('title', 'Raspadinha - ' . $game->scratchCard->name)

@section('content')
<div class="min-h-screen bg-gradient-to-b from-green-400 to-green-600 p-4">
    <!-- Header Banner -->
    <div class="bg-gradient-to-r from-blue-800 to-blue-900 rounded-lg p-4 mb-4 text-center">
        <h1 class="text-yellow-400 font-bold text-xl mb-1">RASPOU ACHOU GANHOU</h1>
        <p class="text-yellow-300 text-sm">ACHE 3 IGUAIS | 🎯 GANHE NA HORA!</p>
    </div>

    <div class="max-w-4xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- Main Game Area -->
        <div class="lg:col-span-2">
            <!-- Scratch Card Container -->
            <div class="flex flex-col w-full justify-center">
                <div class="w-full max-w-[340px] md:max-w-[580px] min-h-[350px] md:min-h-[530px] mx-auto relative rounded-md overflow-hidden">

                    <!-- Game Board (Hidden under scratch layer) -->
                    <div id="game-board" class="absolute inset-0 grid grid-cols-3 gap-2 p-4 w-full h-full bg-white">
                        @for($i = 1; $i <= 9; $i++)
                            <div class="scratch-position bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center text-4xl font-bold shadow-lg border-2 border-yellow-600"
                                 data-position="{{ $i }}"
                                 id="position-{{ $i }}">
                                <span class="symbol-content text-black drop-shadow-lg">
                                    ?
                                </span>
                            </div>
                        @endfor
                    </div>

                    <!-- Scratch Canvas Layer -->
                    <canvas id="scratch-canvas"
                            class="absolute top-0 left-0 z-10 cursor-none"
                            width="340"
                            height="350"
                            style="width: 340px; height: 350px; display: none;">
                    </canvas>

                    <!-- Start Game Overlay -->
                    <div id="start-overlay" class="absolute inset-0 bg-cover bg-center flex items-center justify-center"
                         style="background-image: url('https://worldgamesbr.com.br/wp-content/uploads/2025/07/RASPE-AQUI-1.png');">
                        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20"></div>
                        <button id="start-game-btn" class="bg-red-600 hover:bg-red-700 text-white font-bold px-6 py-2 rounded shadow-md w-[280px] h-[52px] text-lg z-40">
                            Comprar e Raspar (R$ {{ number_format($game->scratchCard->price, 2, ',', '.') }})
                        </button>
                    </div>
                </div>

                <!-- Game Controls -->
                <div class="flex items-center justify-center gap-2 mt-2 md:max-w-[570px] md:min-w-[570px] min-w-[340px] mx-auto">
                    <button id="reveal-all-btn" class="bg-orange-500 hover:bg-orange-600 text-white w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center" style="display: none;">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span>REVELAR</span>
                    </button>

                    <button id="play-btn" class="bg-blue-600 hover:bg-blue-700 text-white w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>JOGAR</span>
                    </button>

                    <div class="w-full rounded-lg font-bold bg-blue-800 h-[45px] md:min-h-[60px] flex flex-col items-center justify-center p-2">
                        <p class="text-blue-300 text-xs">SEU SALDO</p>
                        <span class="text-amber-400 text-sm" id="user-balance">R$ {{ auth()->user()->formatted_balance }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-4">
            <!-- Promotional Banner -->
            <div class="bg-gradient-to-b from-purple-600 to-purple-800 rounded-lg p-4 text-center text-white">
                <div class="mb-2">⭐</div>
                <h3 class="font-bold text-yellow-300 mb-2">VENHA SE DIVERTIR</h3>
                <h4 class="font-bold text-yellow-300 mb-2">E GANHAR VÁRIOS</h4>
                <h4 class="font-bold text-yellow-300 mb-2">PRÊMIOS INSTANTÂNEOS</h4>
                <p class="text-sm mb-2">ALÉM DE CONCORRER</p>
                <p class="text-sm mb-4">A SUPER PRÊMIOS!</p>
                <div class="flex justify-center items-center mb-4">
                    <span class="text-4xl mr-2">🎁</span>
                    <span class="text-4xl">💰</span>
                </div>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    JOGUE AGORA!
                </button>
            </div>

            <!-- Keyboard Shortcuts -->
            <div class="bg-blue-800 rounded-lg p-4">
                <h3 class="text-white font-bold mb-3">ATALHOS DO TECLADO</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">R</span>
                        <span class="text-blue-300">Revelar tudo</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Espaço</span>
                        <span class="text-blue-300">Jogar/Comprar</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Enter</span>
                        <span class="text-blue-300">Confirmar</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Esc</span>
                        <span class="text-blue-300">Cancelar</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startOverlay = document.getElementById('start-overlay');
    const gameBoard = document.getElementById('game-board');
    const startGameBtn = document.getElementById('start-game-btn');
    const playBtn = document.getElementById('play-btn');
    const revealAllBtn = document.getElementById('reveal-all-btn');
    const positions = document.querySelectorAll('.scratch-position');
    const userBalance = document.getElementById('user-balance');

    let gameStarted = false;
    let gameCompleted = {{ $game->status === 'completed' ? 'true' : 'false' }};
    let scratchedPositions = [];

    // Start game
    function startGame() {
        if (gameStarted) return;

        startOverlay.style.display = 'none';
        gameBoard.style.display = 'grid';
        revealAllBtn.style.display = 'flex';
        gameStarted = true;
    }

    // Event listeners
    startGameBtn.addEventListener('click', startGame);
    playBtn.addEventListener('click', startGame);

    // Reveal all positions
    revealAllBtn.addEventListener('click', function() {
        if (!gameStarted || gameCompleted) return;

        positions.forEach(position => {
            if (!scratchedPositions.includes(position.dataset.position)) {
                scratchPosition(position);
            }
        });
    });

    // Scratch individual positions
    positions.forEach(position => {
        position.addEventListener('click', function() {
            if (!gameStarted || gameCompleted) return;
            if (scratchedPositions.includes(this.dataset.position)) return;

            scratchPosition(this);
        });
    });

    function scratchPosition(positionElement) {
        const positionNumber = positionElement.dataset.position;

        fetch(`{{ route('games.scratch', $game) }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                position: positionNumber
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the position with the revealed symbol
                const overlay = positionElement.querySelector('.scratch-overlay');
                overlay.textContent = data.symbol;
                overlay.classList.remove('bg-gray-400');
                overlay.classList.add('bg-white', 'text-black', 'revealed');

                // Add scratch sound effect
                playSound('scratch');

                scratchedPositions.push(positionNumber);

                // Check if game is completed
                if (data.game_completed) {
                    gameCompleted = true;

                    // Update balance
                    userBalance.textContent = `R$ ${data.new_balance.toFixed(2).replace('.', ',')}`;

                    setTimeout(() => {
                        if (data.prize_amount > 0) {
                            // Show prize notification
                            notifications.prize(`🎉 PARABÉNS! VOCÊ GANHOU R$ ${data.prize_amount.toFixed(2).replace('.', ',')}!`, 10000);

                            // Add celebration effect
                            createCelebrationEffect();
                        } else {
                            notifications.info('Que pena! Não foi dessa vez. Tente novamente!');
                        }

                        // Show new achievements if any
                        if (data.new_achievements && data.new_achievements.length > 0) {
                            setTimeout(() => {
                                data.new_achievements.forEach((achievement, index) => {
                                    setTimeout(() => {
                                        showAchievementNotification(achievement);
                                    }, index * 1000);
                                });
                            }, 1000);
                        }

                        // Show play again option after a delay
                        setTimeout(() => {
                            if (confirm('Deseja jogar novamente?')) {
                                window.location.href = '{{ route("scratch-cards.index") }}';
                            }
                        }, data.new_achievements && data.new_achievements.length > 0 ? 4000 : 2000);
                    }, 1500);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            notifications.error('Erro ao processar jogada. Tente novamente.');
        });
    }

    // Celebration effect function
    function createCelebrationEffect() {
        // Create confetti effect
        const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.style.position = 'fixed';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.borderRadius = '50%';
                confetti.style.pointerEvents = 'none';
                confetti.style.zIndex = '9999';
                confetti.style.animation = 'fall 3s linear forwards';

                document.body.appendChild(confetti);

                setTimeout(() => {
                    if (confetti.parentNode) {
                        confetti.parentNode.removeChild(confetti);
                    }
                }, 3000);
            }, i * 100);
        }
    }

    // Add CSS animation for confetti
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fall {
            0% {
                transform: translateY(-10px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .scratch-position:hover {
            transform: scale(1.05);
            transition: transform 0.2s ease;
        }

        .scratch-overlay {
            transition: all 0.3s ease;
        }

        .scratch-overlay.revealed {
            background: linear-gradient(45deg, #FFD700, #FFA500) !important;
            color: #000 !important;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }
    `;
    document.head.appendChild(style);

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        switch(e.key.toLowerCase()) {
            case 'r':
                e.preventDefault();
                if (gameStarted && !gameCompleted) {
                    revealAllBtn.click();
                    notifications.info('Revelando todas as posições...');
                }
                break;
            case ' ':
                e.preventDefault();
                if (!gameStarted) {
                    startGame();
                    notifications.success('Jogo iniciado! Boa sorte!');
                }
                break;
            case 'escape':
                e.preventDefault();
                if (confirm('Deseja sair do jogo?')) {
                    window.location.href = '{{ route("scratch-cards.index") }}';
                }
                break;
        }
    });

    // Show achievement notification
    function showAchievementNotification(achievement) {
        // Create achievement modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-8 max-w-md mx-4 text-center transform scale-0 transition-transform duration-500">
                <div class="text-6xl mb-4">${achievement.icon}</div>
                <h2 class="text-2xl font-bold text-white mb-2">CONQUISTA DESBLOQUEADA!</h2>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">${achievement.name}</h3>
                <p class="text-gray-800 mb-4">${achievement.description}</p>
                <div class="text-lg font-bold text-white">+${achievement.points} pontos</div>
                <button onclick="this.closest('.fixed').remove()" class="mt-4 bg-white text-orange-500 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    Continuar
                </button>
            </div>
        `;

        document.body.appendChild(modal);

        // Animate in
        setTimeout(() => {
            modal.querySelector('.transform').classList.remove('scale-0');
            modal.querySelector('.transform').classList.add('scale-100');
        }, 100);

        // Auto close after 5 seconds
        setTimeout(() => {
            if (modal.parentNode) {
                modal.remove();
            }
        }, 5000);

        // Play achievement sound
        playSound('achievement');
    }

    // Add sound effects (optional)
    function playSound(type) {
        // You can add sound files later
        // const audio = new Audio(`/sounds/${type}.mp3`);
        // audio.play().catch(() => {}); // Ignore errors if sound fails
    }
});
</script>
@endpush
@endsection
