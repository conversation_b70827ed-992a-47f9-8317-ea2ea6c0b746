@extends('layouts.app')

@section('title', 'Raspadinha - ' . $game->scratchCard->name)

@section('content')
<div class="min-h-screen bg-gradient-to-b from-green-400 to-green-600 p-4">
    <!-- Header Banner -->
    <div class="bg-gradient-to-r from-blue-800 to-blue-900 rounded-lg p-4 mb-4 text-center">
        <h1 class="text-yellow-400 font-bold text-xl mb-1">RASPOU ACHOU GANHOU</h1>
        <p class="text-yellow-300 text-sm">ACHE 3 IGUAIS | 🎯 GANHE NA HORA!</p>
    </div>

    <div class="max-w-4xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- Main Game Area -->
        <div class="lg:col-span-2">
            <!-- Scratch Card Container -->
            <div class="flex flex-col w-full justify-center">
                <div class="w-full max-w-[340px] md:max-w-[580px] min-h-[350px] md:min-h-[530px] mx-auto relative rounded-md overflow-hidden">

                    <!-- Game Board (Hidden under scratch layer) -->
                    <div id="game-board" class="absolute inset-0 grid grid-cols-3 gap-2 p-4 w-full h-full bg-white">
                        @for($i = 1; $i <= 9; $i++)
                            <div class="scratch-position bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center text-4xl font-bold shadow-lg border-2 border-yellow-600"
                                 data-position="{{ $i }}"
                                 id="position-{{ $i }}">
                                <span class="symbol-content text-black drop-shadow-lg">
                                    ?
                                </span>
                            </div>
                        @endfor
                    </div>

                    <!-- Scratch Canvas Layer -->
                    <canvas id="scratch-canvas"
                            class="absolute top-0 left-0 z-10 cursor-none"
                            width="340"
                            height="350"
                            style="width: 340px; height: 350px; display: none;">
                    </canvas>

                    <!-- Start Game Overlay -->
                    <div id="start-overlay" class="absolute inset-0 bg-cover bg-center flex items-center justify-center"
                         style="background-image: url('https://worldgamesbr.com.br/wp-content/uploads/2025/07/RASPE-AQUI-1.png');">
                        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20"></div>
                        <button id="start-game-btn" class="bg-red-600 hover:bg-red-700 text-white font-bold px-6 py-2 rounded shadow-md w-[280px] h-[52px] text-lg z-40">
                            Comprar e Raspar (R$ {{ number_format($game->scratchCard->price, 2, ',', '.') }})
                        </button>
                    </div>
                </div>

                <!-- Game Controls -->
                <div class="flex items-center justify-center gap-2 mt-2 md:max-w-[570px] md:min-w-[570px] min-w-[340px] mx-auto">
                    <button id="reveal-all-btn" class="bg-orange-500 hover:bg-orange-600 text-white w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center" style="display: none;">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span>REVELAR</span>
                    </button>

                    <button id="play-btn" class="bg-blue-600 hover:bg-blue-700 text-white w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>JOGAR</span>
                    </button>

                    <div class="w-full rounded-lg font-bold bg-blue-800 h-[45px] md:min-h-[60px] flex flex-col items-center justify-center p-2">
                        <p class="text-blue-300 text-xs">SEU SALDO</p>
                        <span class="text-amber-400 text-sm" id="user-balance">R$ {{ auth()->user()->formatted_balance }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-4">
            <!-- Promotional Banner -->
            <div class="bg-gradient-to-b from-purple-600 to-purple-800 rounded-lg p-4 text-center text-white">
                <div class="mb-2">⭐</div>
                <h3 class="font-bold text-yellow-300 mb-2">VENHA SE DIVERTIR</h3>
                <h4 class="font-bold text-yellow-300 mb-2">E GANHAR VÁRIOS</h4>
                <h4 class="font-bold text-yellow-300 mb-2">PRÊMIOS INSTANTÂNEOS</h4>
                <p class="text-sm mb-2">ALÉM DE CONCORRER</p>
                <p class="text-sm mb-4">A SUPER PRÊMIOS!</p>
                <div class="flex justify-center items-center mb-4">
                    <span class="text-4xl mr-2">🎁</span>
                    <span class="text-4xl">💰</span>
                </div>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    JOGUE AGORA!
                </button>
            </div>

            <!-- Keyboard Shortcuts -->
            <div class="bg-blue-800 rounded-lg p-4">
                <h3 class="text-white font-bold mb-3">ATALHOS DO TECLADO</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">R</span>
                        <span class="text-blue-300">Revelar tudo</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Espaço</span>
                        <span class="text-blue-300">Jogar/Comprar</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Enter</span>
                        <span class="text-blue-300">Confirmar</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Esc</span>
                        <span class="text-blue-300">Cancelar</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startOverlay = document.getElementById('start-overlay');
    const gameBoard = document.getElementById('game-board');
    const startGameBtn = document.getElementById('start-game-btn');
    const playBtn = document.getElementById('play-btn');
    const positions = document.querySelectorAll('.scratch-position');
    const userBalance = document.getElementById('user-balance');

    let gameStarted = false;
    let gameCompleted = {{ $game->status === 'completed' ? 'true' : 'false' }};
    let gameData = null;
    let canvas, ctx;
    let isDrawing = false;
    let scratchedPercentage = 0;

    // Start game
    async function startGame() {
        if (gameStarted) return;

        try {
            const response = await fetch('{{ route("games.scratch.start", $game->id) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                gameData = data.game_data;
                gameStarted = true;

                // Hide start overlay and show scratch canvas
                startOverlay.style.display = 'none';
                document.getElementById('scratch-canvas').style.display = 'block';

                // Update balance
                document.getElementById('user-balance').textContent = 'R$ ' + parseFloat(data.new_balance).toFixed(2).replace('.', ',');

                // Setup scratch canvas and game board
                setupScratchCanvas();
                setupGameBoard();
            } else {
                alert(data.message || 'Erro ao iniciar o jogo');
            }
        } catch (error) {
            console.error('Error starting game:', error);
            alert('Erro ao conectar com o servidor');
        }
    }

    function setupGameBoard() {
        positions.forEach((position, index) => {
            const positionNumber = index + 1;
            const symbol = gameData.symbols[positionNumber - 1];

            // Set the symbol content
            const symbolContent = position.querySelector('.symbol-content');
            if (symbolContent) {
                symbolContent.textContent = symbol;
            }
        });
    }

    function setupScratchCanvas() {
        canvas = document.getElementById('scratch-canvas');
        ctx = canvas.getContext('2d');

        // Set canvas size to match container
        canvas.width = 340;
        canvas.height = 350;

        // Draw scratch surface with "RASPE AQUI" background
        drawScratchSurface();

        // Set up scratch events
        setupScratchEvents();
    }

    function drawScratchSurface() {
        // Fill with gray scratch surface
        ctx.fillStyle = '#9CA3AF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add "RASPE AQUI" text
        ctx.fillStyle = '#374151';
        ctx.font = 'bold 32px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('RASPE AQUI', canvas.width / 2, canvas.height / 2 - 30);

        // Add arrows and hand cursor indication
        ctx.font = 'bold 24px Arial';
        ctx.fillText('← → ↑ ↓', canvas.width / 2, canvas.height / 2 + 20);

        // Add texture/noise for more realistic look
        for (let i = 0; i < 1000; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const opacity = Math.random() * 0.3;
            ctx.fillStyle = `rgba(55, 65, 81, ${opacity})`;
            ctx.fillRect(x, y, 1, 1);
        }
    }

    function setupScratchEvents() {
        // Mouse events
        canvas.addEventListener('mousedown', startScratch);
        canvas.addEventListener('mousemove', scratch);
        canvas.addEventListener('mouseup', stopScratch);
        canvas.addEventListener('mouseleave', stopScratch);

        // Touch events for mobile
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopScratch);

        // Prevent context menu
        canvas.addEventListener('contextmenu', e => e.preventDefault());
    }

    function startScratch(e) {
        isDrawing = true;
        scratch(e);
    }

    function scratch(e) {
        if (!isDrawing) return;

        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        scratchAt(x, y);
    }

    function handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        if (!touch) return;

        const rect = canvas.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;

        if (e.type === 'touchstart') {
            isDrawing = true;
        }

        if (isDrawing) {
            scratchAt(x, y);
        }
    }

    function stopScratch() {
        isDrawing = false;
    }

    function scratchAt(x, y) {
        // Set composite operation to "destination-out" to erase
        ctx.globalCompositeOperation = 'destination-out';
        ctx.beginPath();
        ctx.arc(x, y, 20, 0, 2 * Math.PI);
        ctx.fill();

        // Reset composite operation
        ctx.globalCompositeOperation = 'source-over';

        // Check scratch percentage
        checkScratchProgress();
    }

    function checkScratchProgress() {
        // Sample pixels to check how much has been scratched
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const pixels = imageData.data;
        let transparentPixels = 0;

        // Check every 10th pixel for performance
        for (let i = 3; i < pixels.length; i += 40) {
            if (pixels[i] === 0) { // Alpha channel is 0 (transparent)
                transparentPixels++;
            }
        }

        scratchedPercentage = (transparentPixels / (pixels.length / 40)) * 100;

        // If 30% or more is scratched, reveal the game
        if (scratchedPercentage >= 30) {
            revealGame();
        }
    }

    function revealGame() {
        if (gameCompleted) return;

        // Hide canvas
        canvas.style.display = 'none';

        // Show reveal button
        showRevealButton();
    }

    function showRevealButton() {
        // Create reveal button overlay
        const container = canvas.parentElement;
        const revealBtn = document.createElement('button');
        revealBtn.id = 'reveal-btn';
        revealBtn.className = 'absolute inset-0 bg-orange-500 hover:bg-orange-600 text-white font-bold text-xl flex items-center justify-center z-20 transition-colors';
        revealBtn.innerHTML = '⚡ REVELAR';

        revealBtn.addEventListener('click', function() {
            revealBtn.remove();
            endGame();
        });

        container.appendChild(revealBtn);
    }

    async function endGame() {
        if (gameCompleted) return;
        gameCompleted = true;

        // Determine if won based on symbols
        const won = checkWinCondition();

        try {
            const response = await fetch('{{ route("games.scratch.end", $game->id) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    scratched_positions: [1,2,3,4,5,6,7,8,9], // All positions revealed
                    won: won
                })
            });

            const data = await response.json();

            if (data.success) {
                // Update balance
                document.getElementById('user-balance').textContent = 'R$ ' + parseFloat(data.new_balance).toFixed(2).replace('.', ',');

                if (won) {
                    // Show win message
                    setTimeout(() => {
                        alert('Parabéns! Você ganhou R$ ' + parseFloat(data.prize_amount).toFixed(2).replace('.', ',') + '!');
                    }, 1000);
                } else {
                    // Show lose message
                    setTimeout(() => {
                        alert('Que pena! Tente novamente!');
                    }, 1000);
                }

                // Reset game after 3 seconds
                setTimeout(() => {
                    resetGame();
                }, 3000);
            } else {
                console.error('Error ending game:', data.message);
            }
        } catch (error) {
            console.error('Error ending game:', error);
        }
    }

    function checkWinCondition() {
        // Count symbol occurrences
        const symbolCounts = {};
        gameData.symbols.forEach(symbol => {
            symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
        });

        // Check if any symbol appears 3 or more times
        return Object.values(symbolCounts).some(count => count >= 3);
    }

    function resetGame() {
        gameStarted = false;
        gameData = null;
        gameCompleted = false;
        scratchedPercentage = 0;

        // Reset UI
        startOverlay.style.display = 'flex';
        document.getElementById('scratch-canvas').style.display = 'none';

        // Remove any reveal button
        const revealBtn = document.getElementById('reveal-btn');
        if (revealBtn) {
            revealBtn.remove();
        }

        // Reset all positions
        positions.forEach(position => {
            const symbolContent = position.querySelector('.symbol-content');
            if (symbolContent) {
                symbolContent.textContent = '?';
            }
        });
    }

    // Event listeners
    startGameBtn.addEventListener('click', startGame);
    if (playBtn) playBtn.addEventListener('click', startGame);

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        switch(e.key.toLowerCase()) {
            case ' ':
                e.preventDefault();
                if (!gameStarted) {
                    startGameBtn.click();
                }
                break;
            case 'enter':
                if (!gameStarted) {
                    startGameBtn.click();
                }
                break;
        }
    });
});
</script>
@endpush
@endsection
