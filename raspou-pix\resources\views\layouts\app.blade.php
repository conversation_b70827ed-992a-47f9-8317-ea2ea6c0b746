<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Raspou Pix - Raspadinhas Online')</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        .scratch-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .scratch-position {
            background: linear-gradient(135deg, #c0c0c0 0%, #808080 100%);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .scratch-position:hover {
            transform: scale(1.05);
        }

        .scratch-position.scratched {
            background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
            transform: scale(1.02);
        }

        .balance-card {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        /* Bottom Navigation Styles */
        .text-primary-500 { color: #8b5cf6; }
        .hover\:text-primary-500:hover { color: #8b5cf6; }

        .card {
            background-color: #1f2937;
            border: 1px solid #374151;
            border-radius: 0.75rem;
        }

        .btn-secondary {
            background-color: #374151;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            border: 1px solid #4b5563;
        }

        .btn-secondary:hover {
            background-color: #4b5563;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
        }

        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Adicionar padding bottom ao body para compensar a navegação fixa */
        body {
            padding-bottom: 80px;
        }

        /* Responsividade para navegação inferior */
        @media (max-width: 640px) {
            .bottom-nav-item {
                font-size: 0.7rem;
            }

            .bottom-nav-item svg {
                width: 1.25rem;
                height: 1.25rem;
            }

            .bottom-nav-item i {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Header -->
    <header class="bg-gray-800 shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <h1 class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                        <a href="{{ route('home') }}">🎰 Raspou Pix</a>
                    </h1>
                </div>

                <nav class="hidden md:flex space-x-6">
                    <a href="{{ route('home') }}" class="hover:text-purple-400 transition-colors">Início</a>
                    <a href="{{ route('scratch-cards.index') }}" class="hover:text-purple-400 transition-colors">Raspadinhas</a>
                    <a href="{{ route('leaderboard') }}" class="hover:text-purple-400 transition-colors">Ranking</a>
                    <a href="{{ route('achievements') }}" class="hover:text-purple-400 transition-colors">Conquistas</a>
                    <a href="{{ route('winners') }}" class="hover:text-purple-400 transition-colors">Vencedores</a>
                </nav>

                <div class="flex items-center space-x-4">
                    @auth
                        <div class="balance-card px-4 py-2 rounded-lg text-white font-semibold">
                            <i class="fas fa-wallet mr-2"></i>{{ auth()->user()->formatted_balance }}
                        </div>
                        <div class="relative">
                            <button class="flex items-center space-x-2 hover:text-purple-400 transition-colors">
                                <i class="fas fa-user"></i>
                                <span>{{ auth()->user()->name }}</span>
                            </button>
                        </div>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-red-400 hover:text-red-300 transition-colors">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="btn-primary px-4 py-2 rounded-lg text-white font-semibold">
                            Entrar
                        </a>
                        <a href="{{ route('register') }}" class="border border-purple-400 text-purple-400 px-4 py-2 rounded-lg hover:bg-purple-400 hover:text-white transition-colors">
                            Cadastrar
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        @if (session('success'))
            <div class="bg-green-600 text-white p-4 rounded-lg mb-6 max-w-4xl mx-auto">
                <i class="fas fa-check-circle mr-2"></i>{{ session('success') }}
            </div>
        @endif

        @if (session('error'))
            <div class="bg-red-600 text-white p-4 rounded-lg mb-6 max-w-4xl mx-auto">
                <i class="fas fa-exclamation-circle mr-2"></i>{{ session('error') }}
            </div>
        @endif

        @yield('content')
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700 flex justify-around items-center py-3 z-40">
        <!-- Botão Início -->
        <a href="{{ route('home') }}" class="flex flex-col items-center text-gray-400 hover:text-primary-500 transition {{ request()->routeIs('home') ? 'text-primary-500' : '' }}">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            <span class="text-xs mt-1">Início</span>
        </a>

        <!-- Botão Raspadinhas -->
        <a href="{{ route('scratch-cards.index') }}" class="flex flex-col items-center text-gray-400 hover:text-primary-500 transition {{ request()->routeIs('scratch-cards.index') ? 'text-primary-500' : '' }}">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
            </svg>
            <span class="text-xs mt-1">Raspadinhas</span>
        </a>

        <!-- Botão Números da Sorte -->
        <a href="{{ route('lucky-numbers') }}" class="flex flex-col items-center text-gray-400 hover:text-primary-500 transition {{ request()->routeIs('lucky-numbers') ? 'text-primary-500' : '' }}">
            <i class="fa-solid fa-hashtag" style="font-size: 24px;"></i>
            <span class="text-xs mt-1">Números</span>
        </a>

        @guest
            <!-- Botão Login (se não autenticado) -->
            <a href="{{ route('login') }}" class="flex flex-col items-center text-gray-400 hover:text-primary-500 transition {{ request()->routeIs('login') ? 'text-primary-500' : '' }}">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                </svg>
                <span class="text-xs mt-1">Entrar</span>
            </a>

            <!-- Botão Registro (se não autenticado) -->
            <a href="{{ route('register') }}" class="flex flex-col items-center text-gray-400 hover:text-primary-500 transition {{ request()->routeIs('register') ? 'text-primary-500' : '' }}">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
                <span class="text-xs mt-1">Registrar</span>
            </a>
        @else
            <!-- Botão Carteira (se autenticado) -->
            <a href="{{ route('wallet') }}" class="flex flex-col items-center text-gray-400 hover:text-primary-500 transition {{ request()->routeIs('wallet') ? 'text-primary-500' : '' }}">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                <span class="text-xs mt-1">Carteira</span>
            </a>

            <!-- Botão Perfil (se autenticado) -->
            <a href="{{ route('profile') }}" class="flex flex-col items-center text-gray-400 hover:text-primary-500 transition {{ request()->routeIs('profile') ? 'text-primary-500' : '' }}">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs mt-1">Perfil</span>
            </a>
        @endguest
    </nav>

    <!-- Footer -->
    <footer class="bg-gray-800 mt-16 mb-20">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center text-gray-400">
                <p>&copy; 2024 Raspou Pix. Todos os direitos reservados.</p>
                <p class="mt-2 text-sm">Jogue com responsabilidade. +18 anos.</p>
            </div>
        </div>
    </footer>

    <!-- Notification System -->
    @include('components.notification')

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // CSRF Token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
