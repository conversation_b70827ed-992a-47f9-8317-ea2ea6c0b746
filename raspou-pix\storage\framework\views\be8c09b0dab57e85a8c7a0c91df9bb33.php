<?php $__env->startSection('title', 'Raspadinha - ' . $game->scratchCard->name); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-b from-green-400 to-green-600 p-4">
    <!-- Header Banner -->
    <div class="bg-gradient-to-r from-blue-800 to-blue-900 rounded-lg p-4 mb-4 text-center">
        <h1 class="text-yellow-400 font-bold text-xl mb-1">RASPOU ACHOU GANHOU</h1>
        <p class="text-yellow-300 text-sm">ACHE 3 IGUAIS | 🎯 GANHE NA HORA!</p>
    </div>

    <div class="max-w-4xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- Main Game Area -->
        <div class="lg:col-span-2">
            <!-- Scratch Card Container -->
            <div class="flex flex-col w-full justify-center">
                <div class="w-full max-w-[340px] md:max-w-[580px] min-h-[350px] md:min-h-[530px] mx-auto relative rounded-md overflow-hidden bg-center bg-cover"
                     style="background-image: url('https://worldgamesbr.com.br/wp-content/uploads/2025/07/RASPE-AQUI-1.png'); height: 350px;">

                    <!-- Game Board -->
                    <div id="game-board" class="grid grid-cols-3 gap-2 p-2 bgaff md:min-w-[580px] md:min-h-[530px]" style="width: 340px; height: 350px;">
                        <?php
                            $prizes = [
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/2_reais.png', 'text' => 'R$2,00'],
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/iphone12_white_12897651.webp', 'text' => 'Iphone'],
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/100_reais.jpg', 'text' => 'R$100,00'],
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/fone_bluetooth_c2050b7a.webp', 'text' => 'FONE'],
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/10_reais.png', 'text' => 'R$10,00'],
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/50_reais.png', 'text' => 'R$50,00'],
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/15_reais_5fbfe586.png', 'text' => 'R$15,00'],
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/5_reais.png', 'text' => 'R$5,00'],
                                ['image' => 'https://worldgamesbr.com.br/wp-content/uploads/2025/07/dinheiro_859419ed.webp', 'text' => 'R$1000,00']
                            ];
                        ?>
                        <?php for($i = 1; $i <= 9; $i++): ?>
                            <div class="scratch-position flex flex-col items-center justify-center min-h-[90px] bg-base rounded-md transition-all"
                                 data-position="<?php echo e($i); ?>"
                                 id="position-<?php echo e($i); ?>">
                                <div class="prize-display">
                                    <img src="<?php echo e($prizes[$i-1]['image']); ?>" alt="premio" class="w-[40px] h-[40px] object-contain mb-1">
                                    <span class="text-white text-xs font-semibold text-center"><?php echo e($prizes[$i-1]['text']); ?></span>
                                </div>
                                <div class="result-display hidden text-center">
                                    <span class="symbol-content text-2xl text-white">?</span>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>

                    <!-- Scratch Canvas Layer -->
                    <canvas id="scratch-canvas"
                            class="scratch-area absolute top-0 left-0 z-10"
                            width="340"
                            height="350"
                            style="width: 340px; height: 350px; opacity: 1; cursor: none;">
                    </canvas>

                    <!-- Custom Cursor -->
                    <div class="custom-cursor hidden md:block" style="left: 0px; top: 0px; display: none;">
                        <div class="coin-cursor"></div>
                    </div>
                </div>

                <!-- Result Message -->
                <div id="game-result" class="text-center text-white mt-2 bgaff rounded-lg mb-3 p-3 w-[92%] mx-auto md:max-w-[570px] hidden">
                    <p>😢 Não foi dessa vez</p>
                    <p class="text-white text-center uppercase blink-animation">Mas não desanime, a sorte pode estar na próxima raspadinha!</p>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-center gap-2 mt-2 md:max-w-[570px] md:min-w-[570px] min-w-[340px] mx-auto">
                    <button id="reveal-all-btn" class="bg-[#d97706] w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center" style="display: none;">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span>REVELAR</span>
                    </button>

                    <button id="play-again-btn" class="bgbotaop w-full rounded-lg font-bold h-[45px] md:min-h-[60px] flex items-center justify-center" style="display: none;">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span>JOGAR NOVAMENTE</span>
                    </button>

                    <div class="w-full rounded-lg font-bold bgaff h-[45px] md:min-h-[60px] flex flex-col items-center p-2">
                        <p class="textodesempenho text-xs">SEU SALDO</p>
                        <span class="text-amber-400 text-sm">R$ <span id="user-balance"><?php echo e(number_format(auth()->user()->balance, 2, ',', '.')); ?></span></span>
                    </div>
                </div>


            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1 space-y-4">
            <!-- Promotional Banner -->
            <div class="bg-gradient-to-b from-purple-600 to-purple-800 rounded-lg p-4 text-center text-white">
                <div class="mb-2">⭐</div>
                <h3 class="font-bold text-yellow-300 mb-2">VENHA SE DIVERTIR</h3>
                <h4 class="font-bold text-yellow-300 mb-2">E GANHAR VÁRIOS</h4>
                <h4 class="font-bold text-yellow-300 mb-2">PRÊMIOS INSTANTÂNEOS</h4>
                <p class="text-sm mb-2">ALÉM DE CONCORRER</p>
                <p class="text-sm mb-4">A SUPER PRÊMIOS!</p>
                <div class="flex justify-center items-center mb-4">
                    <span class="text-4xl mr-2">🎁</span>
                    <span class="text-4xl">💰</span>
                </div>
                <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    JOGUE AGORA!
                </button>
            </div>

            <!-- Keyboard Shortcuts -->
            <div class="bg-blue-800 rounded-lg p-4">
                <h3 class="text-white font-bold mb-3">ATALHOS DO TECLADO</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">R</span>
                        <span class="text-blue-300">Revelar tudo</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Espaço</span>
                        <span class="text-blue-300">Jogar/Comprar</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Enter</span>
                        <span class="text-blue-300">Confirmar</span>
                    </div>
                    <div class="flex justify-between items-center text-white">
                        <span class="bg-gray-700 px-2 py-1 rounded">Esc</span>
                        <span class="text-blue-300">Cancelar</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const gameBoard = document.getElementById('game-board');
    const playBtn = document.getElementById('play-btn');
    const positions = document.querySelectorAll('.scratch-position');
    const userBalance = document.getElementById('user-balance');

    let gameStarted = false;
    let gameCompleted = <?php echo e($game->status === 'completed' ? 'true' : 'false'); ?>;
    let gameData = null;
    let canvas, ctx;
    let isDrawing = false;
    let scratchedPercentage = 0;

    // Auto-start game when page loads
    if (!gameCompleted) {
        startGame();
    }

    // Start game
    async function startGame() {
        if (gameStarted) return;

        try {
            const response = await fetch('<?php echo e(route("games.scratch.start", $game->id)); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                gameData = data.game_data;
                gameStarted = true;

                // Update balance
                document.getElementById('user-balance').textContent = 'R$ ' + parseFloat(data.new_balance).toFixed(2).replace('.', ',');

                // Setup scratch canvas and game board
                setupScratchCanvas();
                setupGameBoard();
            } else {
                alert(data.message || 'Erro ao iniciar o jogo');
            }
        } catch (error) {
            console.error('Error starting game:', error);
            alert('Erro ao conectar com o servidor');
        }
    }

    function setupGameBoard() {
        positions.forEach((position, index) => {
            const positionNumber = index + 1;
            const symbol = gameData.symbols[positionNumber - 1];

            // Set the symbol content
            const symbolContent = position.querySelector('.symbol-content');
            if (symbolContent) {
                symbolContent.textContent = symbol;
            }
        });
    }

    function revealResults() {
        // Hide prize displays and show results
        positions.forEach(position => {
            const prizeDisplay = position.querySelector('.prize-display');
            const resultDisplay = position.querySelector('.result-display');

            if (prizeDisplay && resultDisplay) {
                prizeDisplay.classList.add('hidden');
                resultDisplay.classList.remove('hidden');
            }
        });

        // Show game result footer
        const gameResult = document.getElementById('game-result');
        if (gameResult) {
            gameResult.classList.remove('hidden');

            // Update result message based on game outcome
            if (gameData && gameData.prize_amount > 0) {
                gameResult.innerHTML = `
                    <div class="flex items-center justify-center mb-2">
                        <span class="text-yellow-400 text-lg mr-2">🎉</span>
                        <span class="text-white font-bold">PARABÉNS! VOCÊ GANHOU!</span>
                    </div>
                    <p class="text-yellow-400 text-sm font-bold">PRÊMIO: R$ ${gameData.prize_amount.toFixed(2).replace('.', ',')}</p>
                `;
            }
        }
    }

    function setupScratchCanvas() {
        canvas = document.getElementById('scratch-canvas');
        ctx = canvas.getContext('2d');

        // Set canvas size to match container dynamically
        const container = canvas.parentElement;
        const rect = container.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';

        // Draw scratch surface with "RASPE AQUI" background
        drawScratchSurface();

        // Set up scratch events
        setupScratchEvents();
    }

    function drawScratchSurface() {
        // Fill with gray scratch surface
        ctx.fillStyle = '#9CA3AF';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Add "RASPE AQUI" text
        ctx.fillStyle = '#374151';
        ctx.font = 'bold 32px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('RASPE AQUI', canvas.width / 2, canvas.height / 2 - 30);

        // Add arrows and hand cursor indication
        ctx.font = 'bold 24px Arial';
        ctx.fillText('← → ↑ ↓', canvas.width / 2, canvas.height / 2 + 20);

        // Add texture/noise for more realistic look
        for (let i = 0; i < 1000; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const opacity = Math.random() * 0.3;
            ctx.fillStyle = `rgba(55, 65, 81, ${opacity})`;
            ctx.fillRect(x, y, 1, 1);
        }
    }

    function setupScratchEvents() {
        // Mouse events
        canvas.addEventListener('mousedown', startScratch);
        canvas.addEventListener('mousemove', scratch);
        canvas.addEventListener('mouseup', stopScratch);
        canvas.addEventListener('mouseleave', stopScratch);

        // Touch events for mobile
        canvas.addEventListener('touchstart', handleTouch);
        canvas.addEventListener('touchmove', handleTouch);
        canvas.addEventListener('touchend', stopScratch);

        // Prevent context menu
        canvas.addEventListener('contextmenu', e => e.preventDefault());

        // Custom cursor events
        setupCustomCursor();
    }

    function setupCustomCursor() {
        const customCursor = document.querySelector('.custom-cursor');
        if (!customCursor) return;

        canvas.addEventListener('mouseenter', function() {
            customCursor.style.display = 'block';
        });

        canvas.addEventListener('mouseleave', function() {
            customCursor.style.display = 'none';
        });

        canvas.addEventListener('mousemove', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            customCursor.style.left = (rect.left + x) + 'px';
            customCursor.style.top = (rect.top + y) + 'px';
        });

        // Add scratching animation when mouse is down
        canvas.addEventListener('mousedown', function() {
            const coin = customCursor.querySelector('.coin-cursor');
            if (coin) {
                coin.style.transform = 'scale(0.9) rotate(15deg)';
                coin.style.boxShadow = '0 0 15px rgba(255, 215, 0, 0.9), inset 0 2px 4px rgba(255, 255, 255, 0.4)';
            }
        });

        canvas.addEventListener('mouseup', function() {
            const coin = customCursor.querySelector('.coin-cursor');
            if (coin) {
                coin.style.transform = 'scale(1) rotate(0deg)';
                coin.style.boxShadow = '0 0 10px rgba(255, 215, 0, 0.6), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)';
            }
        });
    }

    function startScratch(e) {
        isDrawing = true;
        scratch(e);
    }

    function scratch(e) {
        if (!isDrawing) return;

        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        scratchAt(x, y);
    }

    function handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        if (!touch) return;

        const rect = canvas.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;

        if (e.type === 'touchstart') {
            isDrawing = true;
        }

        if (isDrawing) {
            scratchAt(x, y);
        }
    }

    function stopScratch() {
        isDrawing = false;
    }

    function scratchAt(x, y) {
        // Set composite operation to "destination-out" to erase
        ctx.globalCompositeOperation = 'destination-out';
        ctx.beginPath();
        ctx.arc(x, y, 25, 0, 2 * Math.PI);
        ctx.fill();

        // Add some scratch particles effect
        for (let i = 0; i < 3; i++) {
            const offsetX = (Math.random() - 0.5) * 10;
            const offsetY = (Math.random() - 0.5) * 10;
            ctx.beginPath();
            ctx.arc(x + offsetX, y + offsetY, Math.random() * 8 + 2, 0, 2 * Math.PI);
            ctx.fill();
        }

        // Reset composite operation
        ctx.globalCompositeOperation = 'source-over';

        // Add coin rotation effect during scratching
        const customCursor = document.querySelector('.custom-cursor');
        const coin = customCursor?.querySelector('.coin-cursor');
        if (coin && isDrawing) {
            const rotation = Math.random() * 30 - 15; // Random rotation between -15 and 15 degrees
            coin.style.transform = `scale(0.95) rotate(${rotation}deg)`;

            // Reset rotation after a short delay
            setTimeout(() => {
                if (coin) {
                    coin.style.transform = 'scale(1) rotate(0deg)';
                }
            }, 100);
        }

        // Check scratch percentage
        checkScratchProgress();
    }

    function checkScratchProgress() {
        // Sample pixels to check how much has been scratched
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const pixels = imageData.data;
        let transparentPixels = 0;

        // Check every 10th pixel for performance
        for (let i = 3; i < pixels.length; i += 40) {
            if (pixels[i] === 0) { // Alpha channel is 0 (transparent)
                transparentPixels++;
            }
        }

        scratchedPercentage = (transparentPixels / (pixels.length / 40)) * 100;

        // If 30% or more is scratched, reveal the game
        if (scratchedPercentage >= 30) {
            revealGame();
        }
    }

    function revealGame() {
        if (gameCompleted) return;

        // Hide canvas
        canvas.style.display = 'none';

        // Show reveal button
        const revealBtn = document.getElementById('reveal-all-btn');
        if (revealBtn) {
            revealBtn.style.display = 'flex';
        }
    }



    async function endGame() {
        if (gameCompleted) return;
        gameCompleted = true;

        // Clear the scratch canvas to reveal results
        if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // Show the results
        revealResults();

        // Determine if won based on symbols
        const won = checkWinCondition();

        try {
            const response = await fetch('<?php echo e(route("games.scratch.end", $game->id)); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    scratched_positions: [1,2,3,4,5,6,7,8,9], // All positions revealed
                    won: won
                })
            });

            const data = await response.json();

            if (data.success) {
                // Update balance
                document.getElementById('user-balance').textContent = 'R$ ' + parseFloat(data.new_balance).toFixed(2).replace('.', ',');

                // Update game data with prize amount
                if (data.prize_amount) {
                    gameData.prize_amount = data.prize_amount;

                    // Update the result display if won
                    if (won) {
                        const gameResult = document.getElementById('game-result');
                        if (gameResult) {
                            gameResult.innerHTML = `
                                <div class="flex items-center justify-center mb-2">
                                    <span class="text-yellow-400 text-lg mr-2">🎉</span>
                                    <span class="text-white font-bold">PARABÉNS! VOCÊ GANHOU!</span>
                                </div>
                                <p class="text-yellow-400 text-sm font-bold">PRÊMIO: R$ ${parseFloat(data.prize_amount).toFixed(2).replace('.', ',')}</p>
                            `;
                        }
                    }
                }

                // Hide reveal button and show play again button
                const revealBtn = document.getElementById('reveal-all-btn');
                const playAgainBtn = document.getElementById('play-again-btn');

                if (revealBtn) revealBtn.style.display = 'none';
                if (playAgainBtn) playAgainBtn.style.display = 'flex';
            } else {
                console.error('Error ending game:', data.message);
            }
        } catch (error) {
            console.error('Error ending game:', error);
        }
    }

    function checkWinCondition() {
        // Count symbol occurrences
        const symbolCounts = {};
        gameData.symbols.forEach(symbol => {
            symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
        });

        // Check if any symbol appears 3 or more times
        return Object.values(symbolCounts).some(count => count >= 3);
    }

    function resetGame() {
        gameStarted = false;
        gameData = null;
        gameCompleted = false;
        scratchedPercentage = 0;

        // Remove any reveal button
        const revealBtn = document.getElementById('reveal-btn');
        if (revealBtn) {
            revealBtn.remove();
        }

        // Reset all positions - show prizes again, hide results
        positions.forEach(position => {
            const prizeDisplay = position.querySelector('.prize-display');
            const resultDisplay = position.querySelector('.result-display');
            const symbolContent = position.querySelector('.symbol-content');

            if (prizeDisplay && resultDisplay) {
                prizeDisplay.classList.remove('hidden');
                resultDisplay.classList.add('hidden');
            }

            if (symbolContent) {
                symbolContent.textContent = '?';
            }
        });

        // Hide result footer and buttons
        const gameResult = document.getElementById('game-result');
        const revealBtn = document.getElementById('reveal-all-btn');
        const playAgainBtn = document.getElementById('play-again-btn');

        if (gameResult) gameResult.classList.add('hidden');
        if (revealBtn) revealBtn.style.display = 'none';
        if (playAgainBtn) playAgainBtn.style.display = 'none';

        // Restart the game automatically
        setTimeout(() => {
            startGame();
        }, 1000);
    }

    // Event listeners
    if (playBtn) playBtn.addEventListener('click', startGame);

    const revealAllBtn = document.getElementById('reveal-all-btn');
    if (revealAllBtn) {
        revealAllBtn.addEventListener('click', function() {
            endGame();
        });
    }

    const playAgainBtn = document.getElementById('play-again-btn');
    if (playAgainBtn) {
        playAgainBtn.addEventListener('click', function() {
            // Hide result footer
            const gameResult = document.getElementById('game-result');
            if (gameResult) {
                gameResult.classList.add('hidden');
            }

            // Reset and start new game
            resetGame();
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        switch(e.key.toLowerCase()) {
            case ' ':
                e.preventDefault();
                if (!gameStarted) {
                    startGame();
                }
                break;
            case 'enter':
                if (!gameStarted) {
                    startGame();
                }
                break;
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Classes específicas do layout original */
    .bgaff {
        background: rgba(0, 0, 0, 0.3);
    }

    .bg-base {
        background: rgba(0, 0, 0, 0.4);
    }

    .bgbotaop {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .textodesempenho {
        color: #9ca3af;
    }

    .custom-cursor {
        pointer-events: none;
        position: absolute;
        width: 40px;
        height: 40px;
        z-index: 1000;
        transform: translate(-50%, -50%);
        transition: opacity 0.2s ease;
    }

    .coin-cursor {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%);
        border: 3px solid #b8860b;
        border-radius: 50%;
        box-shadow:
            0 0 10px rgba(255, 215, 0, 0.6),
            inset 0 2px 4px rgba(255, 255, 255, 0.3),
            inset 0 -2px 4px rgba(0, 0, 0, 0.2);
        position: relative;
        animation: coinGlow 2s ease-in-out infinite alternate;
    }

    .coin-cursor::before {
        content: 'R$';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        font-size: 12px;
        color: #8b4513;
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
    }

    .coin-cursor::after {
        content: '';
        position: absolute;
        top: 3px;
        left: 8px;
        width: 8px;
        height: 8px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 50%;
        blur: 1px;
    }

    @keyframes coinGlow {
        0% {
            box-shadow:
                0 0 10px rgba(255, 215, 0, 0.6),
                inset 0 2px 4px rgba(255, 255, 255, 0.3),
                inset 0 -2px 4px rgba(0, 0, 0, 0.2);
        }
        100% {
            box-shadow:
                0 0 20px rgba(255, 215, 0, 0.8),
                inset 0 2px 4px rgba(255, 255, 255, 0.4),
                inset 0 -2px 4px rgba(0, 0, 0, 0.3);
        }
    }

    .scratch-area {
        cursor: none !important;
    }

    .scratch-area:hover + .custom-cursor {
        opacity: 1;
    }

    .blink-animation {
        animation: blink 2s infinite;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.5; }
    }

    .scratch-position {
        transition: all 0.3s ease;
    }

    .scratch-position:hover {
        transform: scale(1.05);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\raspou-pix\resources\views/games/scratch-game.blade.php ENDPATH**/ ?>