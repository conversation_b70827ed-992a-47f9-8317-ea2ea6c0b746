<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Admin') - RaspouPix</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar-active {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-gray-900 text-white">
            <div class="p-4">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-coins text-white"></i>
                    </div>
                    <h1 class="text-xl font-bold">RaspouPix</h1>
                </div>
                <p class="text-gray-400 text-sm mt-1">Painel Administrativo</p>
            </div>
            
            <nav class="mt-8">
                <a href="{{ route('admin.dashboard') }}" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 {{ request()->routeIs('admin.dashboard') ? 'sidebar-active text-white' : '' }}">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a href="{{ route('admin.users') }}" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 {{ request()->routeIs('admin.users') ? 'sidebar-active text-white' : '' }}">
                    <i class="fas fa-users mr-3"></i>
                    Usuários
                </a>
                <a href="{{ route('admin.games') }}" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 {{ request()->routeIs('admin.games') ? 'sidebar-active text-white' : '' }}">
                    <i class="fas fa-gamepad mr-3"></i>
                    Jogos
                </a>
                <a href="{{ route('admin.reports') }}" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 {{ request()->routeIs('admin.reports') ? 'sidebar-active text-white' : '' }}">
                    <i class="fas fa-chart-bar mr-3"></i>
                    Relatórios
                </a>
                <a href="{{ route('admin.settings') }}" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 {{ request()->routeIs('admin.settings') ? 'sidebar-active text-white' : '' }}">
                    <i class="fas fa-cog mr-3"></i>
                    Configurações
                </a>
                
                <div class="border-t border-gray-700 mt-8 pt-4">
                    <a href="{{ route('home') }}" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700">
                        <i class="fas fa-home mr-3"></i>
                        Voltar ao Site
                    </a>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="flex items-center w-full px-4 py-3 text-gray-300 hover:bg-gray-700">
                            <i class="fas fa-sign-out-alt mr-3"></i>
                            Sair
                        </button>
                    </form>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b">
                <div class="flex items-center justify-between px-6 py-4">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">@yield('page-title', 'Dashboard')</h2>
                        <p class="text-gray-600 text-sm">@yield('page-description', 'Visão geral do sistema de raspadinhas')</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Olá, {{ auth()->user()->name }}</p>
                            <p class="text-xs text-gray-500">{{ now()->format('d/m/Y H:i') }}</p>
                        </div>
                        <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white"></i>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <main class="flex-1 overflow-y-auto p-6">
                @if(session('success'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                        {{ session('success') }}
                    </div>
                @endif

                @if(session('error'))
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        {{ session('error') }}
                    </div>
                @endif

                @yield('content')
            </main>
        </div>
    </div>

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.bg-green-100, .bg-red-100');
            alerts.forEach(alert => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            });
        }, 5000);
    </script>
</body>
</html>
